# Testing Cross-Domain Logout on Local Subdomains

## Quick Setup Guide

### 1. Set up hosts file (Choose your OS):

**Windows (Run as Administrator):**
```bash
# Run the provided script
setup-hosts.bat

# Or manually add to C:\Windows\System32\drivers\etc\hosts:
127.0.0.1 localhost
127.0.0.1 testorg.localhost
127.0.0.1 testorg2.localhost
127.0.0.1 testorg3.localhost
127.0.0.1 testorg4.localhost
```

**macOS/Linux:**
```bash
# Run the provided script
sudo ./setup-hosts.sh

# Or manually add to /etc/hosts:
sudo nano /etc/hosts
# Add the same entries as above
```

### 2. Start the development server:
```bash
ng serve --configuration=local-subdomains --port=4200
```

### 3. Test the cross-domain logout:

## Testing Steps

### Step 1: Initial Login
1. Open `http://localhost:4200` in your browser
2. Login with an organization account
3. You should be redirected to your organization's subdomain (e.g., `http://testorg4.localhost:4200`)

### Step 2: Verify Subdomain Access
1. Confirm you're on the subdomain URL
2. Check that the organization dashboard loads correctly
3. Open browser DevTools → Application → Local Storage
4. Verify tokens are present on the subdomain

### Step 3: Test Cross-Domain Logout
1. **Click the logout button** on the subdomain
2. **Watch the browser console** for these logs:
   ```
   OrganizationLayout: On subdomain, initiating cross-domain logout
   AuthService: Initiating cross-domain logout from subdomain
   AuthService: Redirecting to main domain for logout completion
   GuestGuard: Logout-complete route detected, allowing access
   AuthService: Performing comprehensive cleanup
   AuthService: Clearing cookies for domains: ["testorg4.localhost", ".testorg4.localhost", ".localhost", "localhost"]
   LogoutCompleteComponent: Logout completed, redirecting to login
   ```

3. **Verify the redirect flow**:
   - Should briefly show logout loading screen
   - Should redirect to `http://localhost:4200/auth/logout-complete`
   - Should then redirect to `http://localhost:4200/auth/login`

### Step 4: Verify Complete Cleanup
1. **Check localStorage on main domain** (`localhost:4200`):
   - Open DevTools → Application → Local Storage → `http://localhost:4200`
   - Should be completely empty (no tokens, user data, etc.)

2. **Check localStorage on subdomain** (`testorg4.localhost:4200`):
   - Open DevTools → Application → Local Storage → `http://testorg4.localhost:4200`
   - Should be completely empty

3. **Check cookies**:
   - Open DevTools → Application → Cookies
   - Should see no authentication-related cookies on either domain

### Step 5: Verify No Auto-Login
1. **Manually navigate to the subdomain**: `http://testorg4.localhost:4200`
2. Should redirect to login page, NOT auto-login
3. Try accessing `http://localhost:4200/dashboard`
4. Should redirect to login page

## Expected Console Logs

### Successful Cross-Domain Logout:
```
OrganizationLayout: Logging out user
OrganizationLayout: On subdomain, initiating cross-domain logout
AuthService: Initiating cross-domain logout from subdomain
AuthService: User logged out, tokens cleared from localStorage and cookies
AuthService: Redirecting to main domain for logout completion: http://localhost:4200/auth/logout-complete

GuestGuard: Logout-complete route detected, allowing access
LogoutCompleteComponent: Completing cross-domain logout
AuthService: Completing cross-domain logout on main domain
AuthService: User logged out, tokens cleared from localStorage and cookies
AuthService: Performing comprehensive cleanup
AuthService: Clearing cookies for domains: ["localhost", ".localhost", ".localhost", "localhost"]
AuthService: Cleared cookie idToken for domain localhost
AuthService: Cleared cookie refreshToken for domain localhost
AuthService: All authentication cookies cleared
AuthService: Cross-domain logout completed successfully
LogoutCompleteComponent: Logout completed, redirecting to login
```

## Troubleshooting

### Issue: Subdomains don't resolve
**Solution:**
- Restart browser after updating hosts file
- Clear browser DNS cache: `chrome://net-internals/#dns`
- Try incognito/private browsing mode

### Issue: CORS errors persist
**Solution:**
- Verify environment.local-subdomains.ts is being used
- Check that API allows localhost subdomains
- Restart development server

### Issue: Tokens not cleared
**Solution:**
- Check console for cleanup logs
- Manually clear browser data
- Verify comprehensive cleanup is running

### Issue: Still getting auto-login
**Solution:**
- Check that both domains' localStorage is empty
- Clear all browser cookies
- Verify the logout flow completed successfully

## Testing Checklist

- [ ] Hosts file configured correctly
- [ ] Development server running with local-subdomains config
- [ ] Can access main domain (localhost:4200)
- [ ] Can login with organization account
- [ ] Gets redirected to subdomain (testorg4.localhost:4200)
- [ ] Organization dashboard loads on subdomain
- [ ] Logout button triggers cross-domain logout
- [ ] Sees logout loading screen briefly
- [ ] Gets redirected through logout-complete to login
- [ ] Console shows all expected logs
- [ ] localStorage empty on both domains
- [ ] Cookies cleared on both domains
- [ ] No automatic re-login occurs
- [ ] Manual subdomain access redirects to login

## Success Criteria

✅ **No CORS errors during logout**  
✅ **Complete token clearing on both domains**  
✅ **No automatic re-login loops**  
✅ **Smooth redirect flow**  
✅ **Proper console logging**  

If all checklist items pass, the cross-domain logout is working correctly!
