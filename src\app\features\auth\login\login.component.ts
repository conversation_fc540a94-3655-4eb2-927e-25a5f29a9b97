import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';
import { UserDataService } from '../../../core/services/user-data/user-data.service';
import { AlertType } from '../../../shared/components/alert/alert.component';
import { environment } from '../../../../environments/environment';

type AuthMethod = 'password' | 'otp';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  loading = false;
  submitted = false;
  returnUrl: string = '/dashboard';
  error: string = '';
  showAlert = false;
  alertType: AlertType = 'error';
  alertMessage: string = '';

  // Authentication method and OTP
  authMethod: AuthMethod = 'password';
  otpSent = false;
  otpRequestId: string = '';

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private userDataService: UserDataService
  ) {
    // Redirect to dashboard if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  ngOnInit(): void {
    this.loginForm = this.formBuilder.group({
      identifier: ['', [Validators.required]], // Can be email or phone
      password: [''],
      otp: [''],
      rememberMe: [false],
    });

    // Get return URL from route parameters or default to '/dashboard'
    this.returnUrl =
      this.route.snapshot.queryParams['returnUrl'] || '/dashboard';

    this.updateValidators();
  }

  // Convenience getter for easy access to form fields
  get f() {
    return this.loginForm.controls;
  }

  private updateValidators() {
    // Clear all validators first
    Object.keys(this.loginForm.controls).forEach((key) => {
      this.loginForm.get(key)?.clearValidators();
    });

    // Set validators based on auth method
    this.loginForm.get('identifier')?.setValidators([Validators.required]);

    if (this.authMethod === 'password') {
      this.loginForm.get('password')?.setValidators([Validators.required]);
    } else if (this.authMethod === 'otp') {
      if (this.otpSent) {
        this.loginForm
          .get('otp')
          ?.setValidators([Validators.required, Validators.minLength(6)]);
      }
    }

    // Update form validation
    Object.keys(this.loginForm.controls).forEach((key) => {
      this.loginForm.get(key)?.updateValueAndValidity();
    });
  }

  setAuthMethod(method: AuthMethod) {
    this.authMethod = method;
    this.showAlert = false;
    this.otpSent = false;
    this.updateValidators();
  }

  getSubmitButtonText(): string {
    if (this.authMethod === 'otp' && !this.otpSent) {
      return 'Send OTP';
    }
    if (this.authMethod === 'otp' && this.otpSent) {
      return 'Verify & Login';
    }
    return 'Sign In';
  }

  onSubmit() {
    this.submitted = true;
    this.showAlert = false;

    // Stop here if form is invalid
    if (this.loginForm.invalid) {
      return;
    }

    this.loading = true;

    if (this.authMethod === 'otp') {
      this.handleOtpFlow();
    } else {
      this.handlePasswordLogin();
    }
  }

  private handlePasswordLogin() {
    this.authService.login(this.loginForm.value).subscribe({
      next: () => {
        this.handleSuccessfulAuth();
      },
      error: (error) => {
        this.handleError(error);
      },
    });
  }

  private handleOtpFlow() {
    if (!this.otpSent) {
      // Send OTP
      const identifier = this.loginForm.get('identifier')?.value;

      // Check if identifier is a phone number (contains only digits and +)
      const isPhoneNumber = /^[\+]?[0-9\s\-\(\)]+$/.test(identifier);

      if (!isPhoneNumber) {
        this.handleError({
          message: 'OTP authentication is only available for phone numbers.',
        });
        return;
      }

      // For now, show message that OTP is temporarily disabled
      this.handleError({
        message:
          'OTP authentication is temporarily unavailable. Please use password login.',
      });

      // TODO: Implement OTP sending when backend API is ready
      // this.authService.sendPhoneOtp(identifier, 'recaptcha-token').subscribe({
      //   next: (response) => {
      //     this.otpRequestId = response.sessionInfo;
      //     this.otpSent = true;
      //     this.updateValidators();
      //     this.loading = false;
      //     this.showSuccessAlert('OTP sent to your phone number');
      //   },
      //   error: (error) => {
      //     this.handleError(error);
      //   }
      // });
    } else {
      // Verify OTP
      const otp = this.loginForm.get('otp')?.value;

      // TODO: Implement OTP verification when backend API is ready
      // this.authService.verifyPhoneOtp(this.otpRequestId, otp).subscribe({
      //   next: () => {
      //     this.handleSuccessfulAuth();
      //   },
      //   error: (error) => {
      //     this.handleError(error);
      //   }
      // });
    }
  }

  private handleSuccessfulAuth() {
    this.loading = false;
    this.handlePostAuthRouting();
  }

  private handlePostAuthRouting() {
    console.log('=== LOGIN: Starting post-auth routing ===');

    // Force refresh user data to get the latest information
    this.userDataService.refreshUserData().subscribe({
      next: (completeUser) => {
        console.log('=== LOGIN: Fresh user data loaded ===');
        console.log('Complete user object:', completeUser);

        if (!completeUser) {
          console.log('LOGIN: No user data found, going to dashboard');
          this.router.navigate(['/dashboard']);
          return;
        }

        // Log detailed profile information
        console.log('LOGIN: User profile details:', {
          userId: completeUser._id,
          type: completeUser.type,
          hasProfile: !!completeUser.profile,
          profile: completeUser.profile,
        });

        // Check if onboarding is completed using fresh data
        const isOnboardingComplete =
          this.authService.isOnboardingCompletedForUser(completeUser);
        console.log(
          'LOGIN: Onboarding completion result:',
          isOnboardingComplete
        );

        if (!isOnboardingComplete) {
          // Redirect to onboarding if not completed
          console.log(
            'LOGIN: Onboarding not completed, redirecting to onboarding'
          );
          this.router.navigate(['/onboarding']);
          return;
        }

        // User has completed onboarding, route based on type and organization
        console.log('LOGIN: Onboarding completed, routing based on user type');
        try {
          this.routeBasedOnUserType(completeUser);
        } catch (error) {
          console.error('LOGIN: Error in routing logic:', error);
          // Fallback to standard dashboard
          this.router.navigate(['/dashboard']);
        }
      },
      error: (error) => {
        console.error('LOGIN: Error loading user data for routing:', error);
        // Fallback to standard dashboard
        this.router.navigate(['/dashboard']);
      },
    });
  }

  private routeBasedOnUserType(completeUser: any) {
    console.log('=== ROUTING DEBUG ===');
    console.log('Environment:', {
      production: environment.production,
      enableSubdomains: environment.enableSubdomains,
      subdomainPattern: environment.subdomainPattern,
      appDomain: environment.appDomain,
    });
    console.log('Current host:', window.location.host);
    console.log('User type:', completeUser.type);
    console.log('User organizations:', completeUser.organizations);

    // Check if user is currently on a subdomain
    const currentHost = window.location.host;
    const isOnSubdomain = this.isCurrentlyOnSubdomain(currentHost);

    console.log('Subdomain analysis:', {
      currentHost,
      isOnSubdomain,
      enableSubdomains: environment.enableSubdomains,
    });

    if (completeUser.type === 'individual') {
      console.log('Processing individual user routing...');

      // Individual users should NEVER be on organization subdomains
      if (isOnSubdomain && environment.enableSubdomains) {
        console.log(
          '🚨 SECURITY: Individual user detected on subdomain - redirecting to 404'
        );
        this.router.navigate(['/404']);
        return;
      }

      // Individual users with completed onboarding go to standard dashboard
      console.log('Routing individual user to standard dashboard');
      this.router.navigate(['/dashboard']);
    } else if (completeUser.type === 'organization') {
      console.log('Processing organization user routing...');

      // Prioritize default organization, then fall back to first organization
      let targetOrg = completeUser.defaultOrganization;

      if (
        !targetOrg &&
        completeUser.organizations &&
        completeUser.organizations.length > 0
      ) {
        targetOrg = completeUser.organizations[0];
      }

      if (targetOrg) {
        if (targetOrg.subdomain) {
          console.log('Organization user routing:', {
            subdomain: targetOrg.subdomain,
            orgName: targetOrg.name,
            isDefaultOrg: !!completeUser.defaultOrganization,
            enableSubdomains: environment.enableSubdomains,
            environment: environment.production ? 'production' : 'development',
          });

          if (environment.enableSubdomains) {
            // Production: Use actual subdomains
            const subdomainHost = environment.subdomainPattern.replace(
              '{subdomain}',
              targetOrg.subdomain
            );

            if (currentHost === subdomainHost) {
              console.log(
                'Already on correct subdomain, routing to org-dashboard'
              );
              this.router.navigate(['/org-dashboard']);
            } else {
              // Check if user is on a different subdomain they don't have access to
              if (isOnSubdomain) {
                const currentSubdomain =
                  this.extractSubdomainFromHost(currentHost);
                const hasAccessToCurrentSubdomain =
                  this.userHasAccessToSubdomain(completeUser, currentSubdomain);

                if (!hasAccessToCurrentSubdomain) {
                  console.log(
                    '🚨 SECURITY: User on unauthorized subdomain - redirecting to 404'
                  );
                  this.router.navigate(['/404']);
                  return;
                }
              }

              console.log(`Redirecting to user's subdomain: ${subdomainHost}`);
              this.redirectToSubdomainWithTokens(
                subdomainHost,
                targetOrg.subdomain
              );
            }
          } else {
            // Development: Use path-based routing on same domain
            console.log(
              'Development mode: Using organization dashboard on main domain'
            );
            this.router.navigate(['/org-dashboard']);
          }
        } else {
          // Organization user without subdomain - this is an error condition
          console.error('CRITICAL: Organization user has no subdomain!');
          console.log(
            'Organization users MUST have a subdomain. Redirecting to main dashboard as emergency fallback.'
          );
          this.router.navigate(['/dashboard']);
        }
      } else {
        // Organization user but no organizations found, go to standard dashboard
        console.log(
          'Organization user with no organizations, routing to standard dashboard'
        );
        this.router.navigate(['/dashboard']);
      }
    } else {
      // Default fallback for unknown user types
      console.log('Unknown user type, routing to standard dashboard');

      // If on subdomain with unknown user type, redirect to 404
      if (isOnSubdomain && environment.enableSubdomains) {
        console.log('Unknown user type on subdomain - redirecting to 404');
        this.router.navigate(['/404']);
        return;
      }

      this.router.navigate(['/dashboard']);
    }
  }

  private redirectToSubdomainWithTokens(
    subdomainHost: string,
    subdomain: string
  ) {
    // Get current auth tokens
    const accessToken = this.authService.getAccessToken();
    const refreshToken = this.authService.getRefreshToken();

    if (!accessToken || !refreshToken) {
      console.error('No auth tokens available for subdomain redirect');
      this.handleError({
        message: 'Authentication tokens not found. Please login again.',
      });
      return;
    }

    console.log('Redirecting to subdomain with tokens:', {
      subdomain,
      subdomainHost,
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
    });

    // Create subdomain URL with tokens as query parameters
    const protocol = window.location.protocol;
    const subdomainUrl = `${protocol}//${subdomainHost}/auth/subdomain-login?token=${encodeURIComponent(
      accessToken
    )}&refresh=${encodeURIComponent(refreshToken)}&redirect=org-dashboard`;

    console.log('Final subdomain URL:', subdomainUrl);
    window.location.href = subdomainUrl;
  }

  /**
   * Check if the current host is a subdomain
   */
  private isCurrentlyOnSubdomain(host: string): boolean {
    if (!environment.enableSubdomains) {
      return false;
    }

    // Extract the base domain from the subdomain pattern
    // e.g., if pattern is "{subdomain}.digimeet.live", base domain is "digimeet.live"
    const baseDomain = environment.subdomainPattern.replace('{subdomain}.', '');

    // If current host is exactly the base domain, it's not a subdomain
    if (host === baseDomain || host === `www.${baseDomain}`) {
      return false;
    }

    // If current host ends with the base domain but is not the base domain, it's a subdomain
    return host.endsWith(`.${baseDomain}`) && host !== baseDomain;
  }

  /**
   * Extract subdomain from host
   */
  private extractSubdomainFromHost(host: string): string | null {
    if (!environment.enableSubdomains) {
      return null;
    }

    const baseDomain = environment.subdomainPattern.replace('{subdomain}.', '');

    if (host === baseDomain || host === `www.${baseDomain}`) {
      return null;
    }

    if (host.endsWith(`.${baseDomain}`)) {
      return host.replace(`.${baseDomain}`, '');
    }

    return null;
  }

  /**
   * Check if user has access to a specific subdomain
   */
  private userHasAccessToSubdomain(
    completeUser: any,
    subdomain: string | null
  ): boolean {
    if (!subdomain || !completeUser) {
      return false;
    }

    // Individual users should never have access to any subdomain
    if (completeUser.type === 'individual') {
      return false;
    }

    // Organization users should only have access to their organization subdomains
    if (completeUser.type === 'organization') {
      const userOrgs = [
        ...(completeUser.organizations || []),
        ...(completeUser.defaultOrganization
          ? [completeUser.defaultOrganization]
          : []),
      ];

      return userOrgs.some((org: any) => org.subdomain === subdomain);
    }

    return false;
  }

  private storeAuthTokensForSubdomain() {
    // Ensure auth tokens are available for subdomain access
    const accessToken = this.authService.getAccessToken();
    const refreshToken = this.authService.getRefreshToken();

    if (accessToken && refreshToken) {
      // Tokens are already in localStorage, subdomain can access them
      console.log('Auth tokens available for subdomain access');
    } else {
      console.warn('No auth tokens found for subdomain access');
    }
  }

  private handleError(error: any) {
    this.loading = false;
    this.alertMessage = error.message || 'An error occurred. Please try again.';
    this.alertType = 'error';
    this.showAlert = true;
  }
}
