import { Component } from '@angular/core';
import { AuthService } from '../../../core/services/auth/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-logout-debug',
  templateUrl: './logout-debug.component.html',
  styleUrls: ['./logout-debug.component.css']
})
export class LogoutDebugComponent {
  
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Test regular logout
   */
  testRegularLogout(): void {
    console.log('=== TESTING REGULAR LOGOUT ===');
    this.authService.logout();
    setTimeout(() => {
      this.router.navigate(['/auth/login']);
    }, 2000);
  }

  /**
   * Test immediate logout (client-side only)
   */
  testImmediateLogout(): void {
    console.log('=== TESTING IMMEDIATE LOGOUT ===');
    this.authService.logoutImmediate();
    setTimeout(() => {
      this.router.navigate(['/auth/login']);
    }, 1000);
  }

  /**
   * Test force logout (clear all sessions)
   */
  testForceLogout(): void {
    console.log('=== TESTING FORCE LOGOUT (ALL SESSIONS) ===');
    this.authService.forceLogoutAllSessions().subscribe({
      next: (response) => {
        console.log('Force logout successful:', response);
        setTimeout(() => {
          this.router.navigate(['/auth/login']);
        }, 1000);
      },
      error: (error) => {
        console.error('Force logout failed:', error);
        setTimeout(() => {
          this.router.navigate(['/auth/login']);
        }, 1000);
      }
    });
  }

  /**
   * Test cross-domain logout
   */
  testCrossDomainLogout(): void {
    console.log('=== TESTING CROSS-DOMAIN LOGOUT ===');
    this.authService.logoutFromSubdomain();
  }

  /**
   * Clear all localStorage manually
   */
  clearAllStorage(): void {
    console.log('=== CLEARING ALL LOCALSTORAGE ===');
    localStorage.clear();
    console.log('All localStorage cleared');
  }

  /**
   * Show current authentication state
   */
  showAuthState(): void {
    console.log('=== CURRENT AUTH STATE ===');
    console.log('Is Authenticated:', this.authService.isAuthenticated());
    console.log('Access Token:', this.authService.getAccessToken());
    console.log('Refresh Token:', this.authService.getRefreshToken());
    console.log('Current User:', this.authService.getCurrentUser());
    console.log('Complete User:', this.authService.getCompleteUser());
    console.log('LocalStorage contents:', {
      idToken: localStorage.getItem('idToken'),
      refreshToken: localStorage.getItem('refreshToken'),
      current_user: localStorage.getItem('current_user'),
      complete_user: localStorage.getItem('complete_user'),
      token_type: localStorage.getItem('token_type')
    });
  }
}
