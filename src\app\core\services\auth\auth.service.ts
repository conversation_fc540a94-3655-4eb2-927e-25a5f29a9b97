import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, timer, EMPTY } from 'rxjs';
import { catchError, map, tap, switchMap, share } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  User,
  CompleteUser,
  UserCredentials,
  RegistrationData,
  AuthResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  EmailOtpRequest,
  EmailOtpVerify,
  PhoneOtpRequest,
  PhoneOtpVerify,
  ApiError,
} from '../../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser$: Observable<User | null>;
  private completeUserSubject: BehaviorSubject<CompleteUser | null>;
  public completeUser$: Observable<CompleteUser | null>;
  private accessTokenKey = 'access_token';
  private refreshTokenKey = 'refresh_token';
  private tokenTypeKey = 'token_type';
  private userKey = 'current_user';
  private completeUserKey = 'complete_user';
  private tokenExpiryKey = 'token_expiry';

  // Token refresh management
  private refreshTokenRequest$: Observable<AuthResponse> | null = null;
  private refreshTimer: any = null;

  constructor(private http: HttpClient) {
    this.currentUserSubject = new BehaviorSubject<User | null>(
      this.getUserFromStorage()
    );
    this.currentUser$ = this.currentUserSubject.asObservable();

    this.completeUserSubject = new BehaviorSubject<CompleteUser | null>(
      this.getCompleteUserFromStorage()
    );
    this.completeUser$ = this.completeUserSubject.asObservable();

    // Start token refresh timer if user is already authenticated
    if (this.isAuthenticated()) {
      console.log(
        'AuthService: User already authenticated, starting token refresh timer'
      );
      this.scheduleTokenRefresh();
    }
  }

  // Get current user value
  public getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  // Get complete user value
  public getCompleteUser(): CompleteUser | null {
    return this.completeUserSubject.value;
  }

  // Check if user is authenticated (check both localStorage and cookies)
  public isAuthenticated(): boolean {
    const token = this.getAccessToken(); // This now checks both localStorage and cookies
    const isAuth = !!token;
    console.log('AuthService: isAuthenticated check:', {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      isAuthenticated: isAuth,
    });
    return isAuth;
  }

  // Refresh complete user data from server
  public refreshCompleteUserData(): Observable<CompleteUser> {
    return this.http.get<CompleteUser>(`${environment.apiUrl}/users/me`).pipe(
      tap((completeUser) => {
        // Store complete user data
        localStorage.setItem(
          this.completeUserKey,
          JSON.stringify(completeUser)
        );
        this.completeUserSubject.next(completeUser);

        // Also update the basic user data
        const basicUser: User = {
          _id: completeUser._id,
          JWT_UID: '', // This might need to be preserved from existing user
          email: completeUser.email,
          name: completeUser.name || completeUser.email, // Fallback to email if name is undefined
          phone_number: completeUser.phone_number,
          roles: [], // Convert from new role structure if needed
        };

        // Preserve existing JWT_UID if available
        const currentUser = this.getCurrentUser();
        if (currentUser?.JWT_UID) {
          basicUser.JWT_UID = currentUser.JWT_UID;
        }

        localStorage.setItem(this.userKey, JSON.stringify(basicUser));
        this.currentUserSubject.next(basicUser);
      }),
      catchError(this.handleError)
    );
  }

  // Load complete user data if authenticated
  public loadCompleteUserData(): Observable<CompleteUser | null> {
    if (!this.isAuthenticated()) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.refreshCompleteUserData();
  }

  // Check if user has completed onboarding
  public isOnboardingCompleted(): boolean {
    const completeUser = this.getCompleteUser();
    return this.isOnboardingCompletedForUser(completeUser);
  }

  // Check if user has completed onboarding with fresh data
  public isOnboardingCompletedForUser(completeUser: any): boolean {
    if (!completeUser) return false;

    console.log('AuthService: Checking onboarding completion for user:', {
      userId: completeUser._id,
      type: completeUser.type,
      hasProfile: !!completeUser.profile,
    });

    // For individual users, check if required profile fields are completed
    if (completeUser.type === 'individual') {
      const profile = completeUser.profile;

      // Check all required fields for individual onboarding completion
      const hasFullName = !!profile?.fullName;
      const hasJobTitle = !!profile?.jobTitle;
      const hasCompanyName = !!profile?.companyName;
      const hasBio = !!profile?.bio;
      const hasIndustryTags = !!(
        profile?.industryTags && profile.industryTags.length > 0
      );
      const hasNetworkingGoal = !!profile?.networkingGoal;

      const isCompleted =
        hasFullName &&
        hasJobTitle &&
        hasCompanyName &&
        hasBio &&
        hasIndustryTags &&
        hasNetworkingGoal;

      console.log('AuthService: Individual user onboarding check:', {
        fullName: profile?.fullName,
        hasFullName,
        jobTitle: profile?.jobTitle,
        hasJobTitle,
        companyName: profile?.companyName,
        hasCompanyName,
        bio: profile?.bio,
        hasBio,
        industryTags: profile?.industryTags,
        hasIndustryTags,
        networkingGoal: profile?.networkingGoal,
        hasNetworkingGoal,
        isCompleted,
      });
      return isCompleted;
    }

    // For organization users, check if they have organization setup
    if (completeUser.type === 'organization') {
      const hasOrganizations =
        completeUser.organizations && completeUser.organizations.length > 0;
      console.log('AuthService: Organization user onboarding check:', {
        organizationCount: completeUser.organizations?.length || 0,
        hasOrganizations,
      });
      return hasOrganizations;
    }

    // If no type is set, onboarding is not completed
    console.log('AuthService: User has no type set, onboarding not completed');
    return false;
  }

  // Login with email/phone and password
  public login(credentials: UserCredentials): Observable<User> {
    return this.http
      .post<AuthResponse>(`${environment.apiUrl}/auth/login`, credentials)
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        map((response) => response.user),
        catchError(this.handleError)
      );
  }

  // Register new user
  public register(data: RegistrationData): Observable<User> {
    return this.http
      .post<AuthResponse>(`${environment.apiUrl}/users/register`, data)
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        map((response) => response.user),
        catchError(this.handleError)
      );
  }

  // Request password reset
  public requestPasswordReset(email: string): Observable<{ message: string }> {
    const request: PasswordResetRequest = { email };
    return this.http
      .post<{ message: string }>(
        `${environment.apiUrl}/auth/request-reset`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  // Reset password with token
  public resetPassword(
    token: string,
    newPassword: string
  ): Observable<{ message: string }> {
    const request: PasswordResetConfirm = { token, newPassword };
    return this.http
      .post<{ message: string }>(
        `${environment.apiUrl}/auth/reset-password`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  // Email OTP Authentication
  public sendEmailOtp(email: string): Observable<{ message: string }> {
    const request: EmailOtpRequest = { email };
    return this.http
      .post<{ message: string }>(
        `${environment.apiUrl}/oauth/email/send-otp`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  public verifyEmailOtp(email: string, code: string): Observable<AuthResponse> {
    const request: EmailOtpVerify = { email, code };
    return this.http
      .post<AuthResponse>(
        `${environment.apiUrl}/oauth/email/verify-otp`,
        request
      )
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        catchError(this.handleError)
      );
  }

  // Phone OTP Authentication
  public sendPhoneOtp(
    phoneNumber: string,
    recaptchaToken: string
  ): Observable<{ sessionInfo: string }> {
    const request: PhoneOtpRequest = { phoneNumber, recaptchaToken };
    return this.http
      .post<{ sessionInfo: string }>(
        `${environment.apiUrl}/oauth/phone/send-code`,
        request
      )
      .pipe(catchError(this.handleError));
  }

  public verifyPhoneOtp(
    sessionInfo: string,
    code: string
  ): Observable<AuthResponse> {
    const request: PhoneOtpVerify = { sessionInfo, code };
    return this.http
      .post<AuthResponse>(
        `${environment.apiUrl}/oauth/phone/verify-code`,
        request
      )
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        catchError(this.handleError)
      );
  }

  // Logout
  public logout(): void {
    // Clear token refresh timer
    this.clearTokenRefreshTimer();

    localStorage.removeItem(this.accessTokenKey);
    localStorage.removeItem(this.refreshTokenKey);
    localStorage.removeItem(this.tokenTypeKey);
    localStorage.removeItem(this.userKey);
    localStorage.removeItem(this.completeUserKey);
    localStorage.removeItem(this.tokenExpiryKey);

    // Clear cookies for subdomain logout
    this.deleteCookie('idToken');
    this.deleteCookie('refreshToken');

    this.currentUserSubject.next(null);
    this.completeUserSubject.next(null);

    console.log(
      'AuthService: User logged out, tokens cleared from localStorage and cookies'
    );
  }

  /**
   * Cross-domain logout for subdomain users
   * This method handles logout from subdomains by redirecting to main domain
   * with a logout flag to ensure tokens are cleared from both domains
   */
  public logoutFromSubdomain(): void {
    console.log('AuthService: Initiating cross-domain logout from subdomain');

    // First clear tokens from current subdomain
    this.logout();

    // Redirect to main domain with logout completion flag
    const protocol = window.location.protocol;
    const mainDomain = `${protocol}//${environment.appDomain}`;
    const logoutUrl = `${mainDomain}/auth/logout-complete`;

    console.log(
      'AuthService: Redirecting to main domain for logout completion:',
      logoutUrl
    );
    window.location.href = logoutUrl;
  }

  /**
   * Complete logout process on main domain
   * This method is called when user is redirected from subdomain logout
   */
  public completeLogoutFromSubdomain(): void {
    console.log('AuthService: Completing cross-domain logout on main domain');

    // Clear all tokens and data from main domain
    this.logout();

    // Additional comprehensive cleanup for cross-domain logout
    this.performComprehensiveCleanup();

    console.log('AuthService: Cross-domain logout completed successfully');
  }

  /**
   * Perform comprehensive cleanup of all stored data
   */
  private performComprehensiveCleanup(): void {
    console.log('AuthService: Performing comprehensive cleanup');

    // Clear any subdomain-related data that might be stored
    const keysToRemove = [
      'lastSubdomain',
      'lastOrganization',
      'selectedOrganization',
      'currentOrganization',
      'organizationData',
      'userPreferences',
      'dashboardState',
      'navigationState',
      // Clear any potential token variations
      'idToken',
      'refreshToken',
      'accessToken',
      'authToken',
      'token',
      // Clear any cached user data variations
      'userData',
      'userInfo',
      'profile',
      'completeUserData',
    ];

    keysToRemove.forEach((key) => {
      try {
        localStorage.removeItem(key);
        console.log(`AuthService: Cleared localStorage key: ${key}`);
      } catch (error) {
        console.warn(
          `AuthService: Failed to clear localStorage key ${key}:`,
          error
        );
      }
    });

    // Clear all cookies with different domain variations
    this.clearAllAuthCookies();

    console.log('AuthService: Comprehensive cleanup completed');
  }

  /**
   * Clear all authentication-related cookies
   */
  private clearAllAuthCookies(): void {
    const cookiesToClear = [
      'idToken',
      'refreshToken',
      'accessToken',
      'authToken',
    ];
    const domains = [
      window.location.hostname,
      `.${window.location.hostname}`,
      '.digimeet.live',
      'digimeet.live',
      '.localhost',
      'localhost',
    ];

    cookiesToClear.forEach((cookieName) => {
      domains.forEach((domain) => {
        try {
          // Clear cookie for each domain variation
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/;`;
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/; secure;`;
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/; samesite=lax;`;
        } catch (error) {
          // Ignore cookie clearing errors
        }
      });
    });

    console.log('AuthService: All authentication cookies cleared');
  }

  // Get access token (check localStorage first, then cookies)
  public getAccessToken(): string | null {
    let token = localStorage.getItem(this.accessTokenKey);
    if (!token) {
      token = this.getCookie('idToken');
      if (token) {
        // If found in cookie, also store in localStorage for consistency
        localStorage.setItem(this.accessTokenKey, token);
        console.log('AuthService: Retrieved access token from cookie');
      }
    }
    return token;
  }

  // Get refresh token (check localStorage first, then cookies)
  public getRefreshToken(): string | null {
    let token = localStorage.getItem(this.refreshTokenKey);
    if (!token) {
      token = this.getCookie('refreshToken');
      if (token) {
        // If found in cookie, also store in localStorage for consistency
        localStorage.setItem(this.refreshTokenKey, token);
        console.log('AuthService: Retrieved refresh token from cookie');
      }
    }
    return token;
  }

  // Get token type
  public getTokenType(): string | null {
    return localStorage.getItem(this.tokenTypeKey);
  }

  // Handle authentication response
  private handleAuthentication(response: AuthResponse): void {
    const { user } = response;

    // Handle both new and legacy token formats
    const accessToken = response.idToken || response.access_token;
    const refreshToken = response.refreshToken || response.refresh_token;
    const tokenType = response.token_type || 'Bearer';

    if (!accessToken || !refreshToken) {
      throw new Error('Invalid authentication response: missing tokens');
    }

    // Store tokens and user in local storage
    localStorage.setItem(this.accessTokenKey, accessToken);
    localStorage.setItem(this.refreshTokenKey, refreshToken);
    localStorage.setItem(this.tokenTypeKey, tokenType);
    localStorage.setItem(this.userKey, JSON.stringify(user));

    // Store tokens in cookies for subdomain sharing
    this.setCookieForSubdomain('idToken', accessToken);
    this.setCookieForSubdomain('refreshToken', refreshToken);

    console.log(
      'AuthService: Tokens stored in both localStorage and cookies for subdomain access'
    );

    // Update current user subject
    this.currentUserSubject.next(user);

    // Start automatic token refresh
    this.scheduleTokenRefresh();

    // After successful authentication, load complete user data
    this.refreshCompleteUserData().subscribe({
      next: (completeUser) => {
        // Complete user data loaded successfully
        console.log(
          'Complete user data loaded after authentication:',
          completeUser
        );
      },
      error: (error) => {
        // Log error but don't fail authentication
        console.warn(
          'Failed to load complete user data after authentication:',
          error
        );
      },
    });
  }

  // Get user from storage
  private getUserFromStorage(): User | null {
    const userJson = localStorage.getItem(this.userKey);
    return userJson ? JSON.parse(userJson) : null;
  }

  // Get complete user from storage
  private getCompleteUserFromStorage(): CompleteUser | null {
    const completeUserJson = localStorage.getItem(this.completeUserKey);
    return completeUserJson ? JSON.parse(completeUserJson) : null;
  }

  // Handle API errors
  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      const apiError = error.error as ApiError;
      if (apiError.error) {
        errorMessage = apiError.error;
      } else if (apiError.message) {
        errorMessage = apiError.message;
      } else if (apiError.details) {
        errorMessage = apiError.details;
      } else {
        errorMessage = error.statusText;
      }
    }

    return throwError(() => new Error(errorMessage));
  }

  // Token refresh functionality
  public refreshToken(): Observable<AuthResponse> {
    const refreshToken = this.getRefreshToken();

    if (!refreshToken) {
      console.error('AuthService: No refresh token available');
      this.logout();
      return throwError(() => new Error('No refresh token available'));
    }

    // If there's already a refresh request in progress, return it
    if (this.refreshTokenRequest$) {
      console.log('AuthService: Refresh token request already in progress');
      return this.refreshTokenRequest$;
    }

    console.log('AuthService: Refreshing access token...');

    this.refreshTokenRequest$ = this.http
      .post<AuthResponse>(`${environment.apiUrl}/auth/refresh`, {
        refreshToken,
      })
      .pipe(
        tap((response) => {
          console.log('AuthService: Token refresh successful');
          this.handleAuthentication(response);
          this.scheduleTokenRefresh();
        }),
        catchError((error) => {
          console.error('AuthService: Token refresh failed:', error);
          this.logout();
          return throwError(() => new Error('Token refresh failed'));
        }),
        share() // Share the observable to prevent multiple requests
      );

    // Clear the request after completion
    this.refreshTokenRequest$.subscribe({
      complete: () => {
        this.refreshTokenRequest$ = null;
      },
      error: () => {
        this.refreshTokenRequest$ = null;
      },
    });

    return this.refreshTokenRequest$;
  }

  private scheduleTokenRefresh(): void {
    // Clear existing timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Schedule refresh 5 minutes before expiry (55 minutes for 1-hour tokens)
    const refreshInterval = 55 * 60 * 1000; // 55 minutes in milliseconds

    console.log(
      `AuthService: Scheduling token refresh in ${
        refreshInterval / 1000 / 60
      } minutes`
    );

    this.refreshTimer = setTimeout(() => {
      console.log('AuthService: Auto-refreshing token...');
      this.refreshToken().subscribe({
        next: () => {
          console.log('AuthService: Auto token refresh successful');
        },
        error: (error) => {
          console.error('AuthService: Auto token refresh failed:', error);
        },
      });
    }, refreshInterval);
  }

  private clearTokenRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // Check if token needs refresh (call this before API requests)
  public ensureValidToken(): Observable<string | null> {
    const token = this.getAccessToken();

    if (!token) {
      return throwError(() => new Error('No access token available'));
    }

    // For now, we'll refresh proactively since we don't have expiry info
    // In a real app, you'd decode the JWT to check expiry
    return new Observable((observer) => {
      observer.next(token);
      observer.complete();
    });
  }

  // Cookie methods for subdomain authentication
  private setCookieForSubdomain(name: string, value: string): void {
    // Set cookie with domain scope for subdomain sharing
    const domain = this.getCookieDomain();
    const expires = new Date();
    expires.setTime(expires.getTime() + 24 * 60 * 60 * 1000); // 24 hours

    const cookieString = `${name}=${value}; expires=${expires.toUTCString()}; domain=${domain}; path=/; SameSite=Lax`;
    document.cookie = cookieString;

    console.log(`AuthService: Set cookie ${name} for domain ${domain}`);
  }

  private getCookieDomain(): string {
    const hostname = window.location.hostname;

    // For localhost development, use .localhost
    if (hostname.includes('localhost')) {
      return '.localhost';
    }

    // For production, use .digimeet.live
    if (hostname.includes('digimeet.live')) {
      return '.digimeet.live';
    }

    // Fallback to current hostname
    return hostname;
  }

  private getCookie(name: string): string | null {
    const nameEQ = name + '=';
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  private deleteCookie(name: string): void {
    const domain = this.getCookieDomain();
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=${domain}; path=/;`;
  }
}
