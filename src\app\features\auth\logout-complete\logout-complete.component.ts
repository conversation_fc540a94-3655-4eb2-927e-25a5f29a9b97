import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth/auth.service';

@Component({
  selector: 'app-logout-complete',
  templateUrl: './logout-complete.component.html',
  styleUrls: ['./logout-complete.component.css'],
})
export class LogoutCompleteComponent implements OnInit {
  constructor(private authService: AuthService, private router: Router) {}

  ngOnInit(): void {
    console.log('LogoutCompleteComponent: Completing cross-domain logout');
    
    // Complete the logout process on main domain
    this.authService.completeLogoutFromSubdomain();
    
    // Small delay to ensure cleanup is complete, then redirect to login
    setTimeout(() => {
      console.log('LogoutCompleteComponent: Redirecting to login page');
      this.router.navigate(['/auth/login']);
    }, 500);
  }
}
