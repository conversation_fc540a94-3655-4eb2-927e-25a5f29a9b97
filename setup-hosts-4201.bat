@echo off
echo Setting up local subdomain hosts for testing on port 4201...
echo.
echo This script will add entries to your hosts file for local subdomain testing.
echo You need to run this as Administrator.
echo.
pause

set HOSTS_FILE=%SystemRoot%\System32\drivers\etc\hosts

echo.
echo Adding localhost subdomain entries...

echo # Local subdomain testing entries for IFTA project (port 4201) >> %HOSTS_FILE%
echo 127.0.0.1 localhost >> %HOSTS_FILE%
echo 127.0.0.1 testorg.localhost >> %HOSTS_FILE%
echo 127.0.0.1 testorg2.localhost >> %HOSTS_FILE%
echo 127.0.0.1 testorg3.localhost >> %HOSTS_FILE%
echo 127.0.0.1 testorg4.localhost >> %HOSTS_FILE%
echo 127.0.0.1 myorg.localhost >> %HOSTS_FILE%

echo.
echo Hosts file updated successfully!
echo.
echo You can now test local subdomains:
echo - Main domain: http://localhost:4201
echo - Test org: http://testorg.localhost:4201
echo - Test org 2: http://testorg2.localhost:4201
echo - Test org 3: http://testorg3.localhost:4201
echo - Test org 4: http://testorg4.localhost:4201
echo - My org: http://myorg.localhost:4201
echo.
echo Next steps:
echo 1. Start the dev server: ng serve --configuration=local-subdomains --port=4201
echo 2. Open http://localhost:4201 in your browser
echo 3. Login with an organization account
echo 4. Test the cross-domain logout functionality
echo.
pause
