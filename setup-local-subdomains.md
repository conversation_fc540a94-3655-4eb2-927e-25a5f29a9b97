# Local Subdomain Testing Setup

This guide will help you test the cross-domain logout functionality on localhost with subdomains.

## Step 1: Configure Your Hosts File

Add these entries to your hosts file to enable local subdomains:

### Windows (`C:\Windows\System32\drivers\etc\hosts`):
```
127.0.0.1 localhost
127.0.0.1 testorg.localhost
127.0.0.1 testorg2.localhost
127.0.0.1 testorg3.localhost
127.0.0.1 testorg4.localhost
```

### macOS/Linux (`/etc/hosts`):
```
127.0.0.1 localhost
127.0.0.1 testorg.localhost
127.0.0.1 testorg2.localhost
127.0.0.1 testorg3.localhost
127.0.0.1 testorg4.localhost
```

## Step 2: Build with Local Subdomain Environment

```bash
# Build the app with local subdomain configuration
ng build --configuration=local-subdomains

# Or serve with local subdomain configuration
ng serve --configuration=local-subdomains --port=4200
```

## Step 3: Configure Angular for Local Subdomains

Update your `angular.json` to include the local-subdomains configuration:

```json
"configurations": {
  "local-subdomains": {
    "fileReplacements": [
      {
        "replace": "src/environments/environment.ts",
        "with": "src/environments/environment.local-subdomains.ts"
      }
    ]
  }
}
```

## Step 4: Test the Cross-Domain Logout

1. **Start the development server:**
   ```bash
   ng serve --configuration=local-subdomains --port=4200
   ```

2. **Access the main domain:**
   - Open: `http://localhost:4200`
   - Login with an organization account

3. **Navigate to subdomain:**
   - You should be redirected to: `http://testorg.localhost:4200` (or your org's subdomain)
   - Verify you can access the organization dashboard

4. **Test logout:**
   - Click the logout button on the subdomain
   - Watch the browser console for logs
   - Verify you're redirected to: `http://localhost:4200/auth/login`
   - Check localStorage on both domains - should be empty

## Step 5: Verify the Fix

### Check Browser Console Logs:
```
GuestGuard: Logout-complete route detected, allowing access
AuthService: Initiating cross-domain logout from subdomain
AuthService: Performing comprehensive cleanup
AuthService: Clearing cookies for domains: ["testorg.localhost", ".testorg.localhost", ".localhost", "localhost"]
AuthService: All authentication cookies cleared
LogoutCompleteComponent: Logout completed, redirecting to login
```

### Check localStorage:
- **On `localhost:4200`**: Should be completely empty
- **On `testorg.localhost:4200`**: Should be completely empty

### Verify No Re-login:
- After logout, manually navigate to `http://testorg.localhost:4200`
- Should redirect to login page, not auto-login

## Troubleshooting

### If subdomains don't work:
1. Clear browser cache and cookies
2. Restart browser after updating hosts file
3. Try incognito/private browsing mode
4. Check that the hosts file was saved correctly

### If CORS errors persist:
1. Check that the API allows requests from localhost subdomains
2. Verify the environment configuration is correct
3. Check browser network tab for actual request origins

### If tokens aren't cleared:
1. Check browser console for cleanup logs
2. Manually inspect localStorage and cookies
3. Verify the comprehensive cleanup is running

## Testing Checklist

- [ ] Hosts file configured
- [ ] Angular configuration updated
- [ ] App built with local-subdomains config
- [ ] Can access main domain (localhost:4200)
- [ ] Can login with organization account
- [ ] Gets redirected to subdomain (testorg.localhost:4200)
- [ ] Can access organization dashboard
- [ ] Logout button works on subdomain
- [ ] Sees logout loading screen
- [ ] Gets redirected to main domain login
- [ ] localStorage is empty on both domains
- [ ] No automatic re-login occurs
- [ ] Console shows proper cleanup logs
