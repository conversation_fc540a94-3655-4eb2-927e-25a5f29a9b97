import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRe<PERSON>,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth/auth.service';
import { SubdomainService } from '../services/subdomain/subdomain.service';
import { environment } from '../../../environments/environment';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private authService: AuthService,
    private subdomainService: SubdomainService
  ) {}

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>ttpHand<PERSON>
  ): Observable<HttpEvent<unknown>> {
    // Get the auth token and token type
    const token = this.authService.getAccessToken();
    const tokenType = this.authService.getTokenType() || 'Bearer';

    // Prepare headers object
    const headers: { [key: string]: string } = {};

    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `${tokenType} ${token}`;
    }

    // Add origin information to help backend determine correct CORS headers
    const currentOrigin = window.location.origin;
    const currentHost = window.location.host;

    // Determine if we're on main domain or subdomain
    const isOnSubdomain =
      this.subdomainService.areSubdomainsEnabled() &&
      this.subdomainService.getCurrentSubdomain();

    // Add custom headers to help backend identify the request origin
    headers['X-Request-Origin'] = currentOrigin;
    headers['X-Request-Host'] = currentHost;

    if (isOnSubdomain) {
      const subdomain = this.subdomainService.getCurrentSubdomain();
      headers['X-Subdomain'] = subdomain || '';
      console.log('AuthInterceptor: Adding subdomain header:', subdomain);
    } else {
      headers['X-Main-Domain'] = 'true';
      console.log('AuthInterceptor: Request from main domain');
    }

    // Clone the request with headers
    if (Object.keys(headers).length > 0) {
      request = request.clone({
        setHeaders: headers,
      });
    }

    console.log('AuthInterceptor: Request headers:', {
      hasAuth: !!token,
      origin: currentOrigin,
      host: currentHost,
      isOnSubdomain,
      headers: Object.keys(headers),
    });

    // Pass the cloned request to the next handler
    return next.handle(request);
  }
}
