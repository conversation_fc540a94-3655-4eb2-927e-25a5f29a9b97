import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {}

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>ttp<PERSON><PERSON><PERSON>
  ): Observable<HttpEvent<unknown>> {
    // Get the auth token and token type
    const token = this.authService.getAccessToken();
    const tokenType = this.authService.getTokenType() || 'Bearer';

    // If token exists, clone the request and add the authorization header
    if (token) {
      request = request.clone({
        setHeaders: {
          Authorization: `${tokenType} ${token}`,
        },
      });
    }

    // Pass the cloned request to the next handler
    return next.handle(request);
  }
}
