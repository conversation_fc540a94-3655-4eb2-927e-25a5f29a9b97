import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {}

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>ttp<PERSON>and<PERSON>
  ): Observable<HttpEvent<unknown>> {
    // Get the auth token and token type
    const token = this.authService.getAccessToken();
    const tokenType = this.authService.getTokenType() || 'Bearer';

    // Debug logging for API requests
    if (request.url.includes('/api/')) {
      console.log('AuthInterceptor: API request details:', {
        url: request.url,
        method: request.method,
        hasToken: !!token,
        tokenLength: token?.length,
        tokenType,
        tokenPreview: token ? token.substring(0, 20) + '...' : 'No token',
      });
    }

    // If token exists, clone the request and add the authorization header
    if (token) {
      request = request.clone({
        setHeaders: {
          Authorization: `${tokenType} ${token}`,
        },
      });

      if (request.url.includes('/api/')) {
        console.log('AuthInterceptor: Added Authorization header to request');
      }
    } else if (request.url.includes('/api/')) {
      console.warn(
        'AuthInterceptor: No token available for API request to:',
        request.url
      );
    }

    // Pass the cloned request to the next handler
    return next.handle(request);
  }
}
