#!/bin/bash

echo "Setting up local subdomain hosts for testing..."
echo ""
echo "This script will add entries to your hosts file for local subdomain testing."
echo "You may need to run this with sudo."
echo ""

HOSTS_FILE="/etc/hosts"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script with sudo:"
    echo "sudo ./setup-hosts.sh"
    exit 1
fi

echo "Adding localhost subdomain entries..."

# Add entries to hosts file
cat >> $HOSTS_FILE << EOF

# Local subdomain testing entries for IFTA project
127.0.0.1 localhost
127.0.0.1 testorg.localhost
127.0.0.1 testorg2.localhost
127.0.0.1 testorg3.localhost
127.0.0.1 testorg4.localhost
127.0.0.1 myorg.localhost
EOF

echo ""
echo "Hosts file updated successfully!"
echo ""
echo "You can now test local subdomains:"
echo "- Main domain: http://localhost:4200"
echo "- Test org: http://testorg.localhost:4200"
echo "- Test org 2: http://testorg2.localhost:4200"
echo "- Test org 3: http://testorg3.localhost:4200"
echo "- Test org 4: http://testorg4.localhost:4200"
echo "- My org: http://myorg.localhost:4200"
echo ""
echo "Next steps:"
echo "1. Start the dev server: ng serve --configuration=local-subdomains --port=4200"
echo "2. Open http://localhost:4200 in your browser"
echo "3. Login with an organization account"
echo "4. Test the cross-domain logout functionality"
echo ""
