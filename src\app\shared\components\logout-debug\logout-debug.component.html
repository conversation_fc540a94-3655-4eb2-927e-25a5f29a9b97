<div class="logout-debug-panel bg-gray-100 p-4 rounded-lg shadow-md">
  <h3 class="text-lg font-bold mb-4 text-gray-800">🔧 Logout Debug Panel</h3>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    
    <!-- Regular Logout Tests -->
    <div class="bg-white p-4 rounded border">
      <h4 class="font-semibold mb-3 text-blue-600">Regular Logout Tests</h4>
      
      <button 
        (click)="testRegularLogout()" 
        class="w-full mb-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
        🔄 Test Regular Logout
      </button>
      
      <button 
        (click)="testImmediateLogout()" 
        class="w-full mb-2 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors">
        ⚡ Test Immediate Logout
      </button>
      
      <button 
        (click)="testForceLogout()" 
        class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
        💥 Test Force Logout (All Sessions)
      </button>
    </div>

    <!-- Cross-Domain Tests -->
    <div class="bg-white p-4 rounded border">
      <h4 class="font-semibold mb-3 text-purple-600">Cross-Domain Tests</h4>
      
      <button 
        (click)="testCrossDomainLogout()" 
        class="w-full mb-2 px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors">
        🌐 Test Cross-Domain Logout
      </button>
      
      <button 
        (click)="clearAllStorage()" 
        class="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
        🗑️ Clear All Storage
      </button>
    </div>

    <!-- Debug Information -->
    <div class="bg-white p-4 rounded border md:col-span-2">
      <h4 class="font-semibold mb-3 text-green-600">Debug Information</h4>
      
      <button 
        (click)="showAuthState()" 
        class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
        📊 Show Current Auth State (Check Console)
      </button>
    </div>

  </div>

  <!-- Instructions -->
  <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
    <h5 class="font-semibold text-yellow-800 mb-2">📝 Instructions:</h5>
    <ul class="text-sm text-yellow-700 space-y-1">
      <li>• <strong>Regular Logout:</strong> Calls backend API + client cleanup</li>
      <li>• <strong>Immediate Logout:</strong> Client cleanup only (emergency)</li>
      <li>• <strong>Force Logout:</strong> Clears ALL user sessions on backend</li>
      <li>• <strong>Cross-Domain:</strong> Tests subdomain → main domain logout</li>
      <li>• <strong>Show Auth State:</strong> Displays current tokens/user data in console</li>
      <li>• Open browser console (F12) to see detailed logs</li>
    </ul>
  </div>

</div>
