import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Components
import { HeaderComponent } from './components/header/header.component';
import { FooterComponent } from './components/footer/footer.component';
import { AlertComponent } from './components/alert/alert.component';
import { LoaderComponent } from './components/loader/loader.component';
import { PaginationComponent } from './components/pagination/pagination.component';
import { CardComponent } from './components/card/card.component';
import { LinkComponent } from './components/link/link.component';
import { ButtonComponent } from './components/button/button.component';
import { CarouselComponent } from './components/carousel/carousel.component';
import { FormComponent } from './components/form/form.component';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { NotFoundComponent } from './components/not-found/not-found.component';
import { UserProfileComponent } from './components/user-profile/user-profile.component';
import { OrganizationManagementComponent } from './components/organization-management/organization-management.component';
import { OrganizationSwitcherComponent } from './components/organization-switcher/organization-switcher.component';
import { SubdomainTestComponent } from './components/subdomain-test/subdomain-test.component';

// Pipes
// Add custom pipes here

// Directives
import { ClickOutsideDirective } from './directives/click-outside.directive';

@NgModule({
  declarations: [
    HeaderComponent,
    FooterComponent,
    AlertComponent,
    LoaderComponent,
    PaginationComponent,
    CardComponent,
    LinkComponent,
    ButtonComponent,
    CarouselComponent,
    FormComponent,
    NotFoundComponent,
    UserProfileComponent,
    OrganizationManagementComponent,
    OrganizationSwitcherComponent,
    SubdomainTestComponent,

    // Directives
    ClickOutsideDirective,
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    SlickCarouselModule,
  ],
  exports: [
    // Export modules
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,

    // Export components
    HeaderComponent,
    FooterComponent,
    AlertComponent,
    LoaderComponent,
    PaginationComponent,
    CardComponent,
    LinkComponent,
    ButtonComponent,
    CarouselComponent,
    FormComponent,
    NotFoundComponent,
    UserProfileComponent,
    OrganizationManagementComponent,
    OrganizationSwitcherComponent,
    SubdomainTestComponent,

    // Directives
    ClickOutsideDirective,
  ],
})
export class SharedModule {}
